# 发票验证功能使用指南

## 概述

PDF保单和发票验证器现已支持完整的发票验证功能，可以验证XML发票和PDF发票，自动识别个人发票、企业发票和政府发票类型。

## 新增功能

### 1. 发票类型自动识别

**个人发票**
- 抬头：`产品测试` 或 `个人产品测试`
- 纳税人识别号：空或不存在

**企业发票**
- 中国太平洋保险（集团）股份有限公司虚拟单位 - `919900000000214267`
- 北京十斤文化传媒有限公司 - `91110101MA01JDE656`

**政府发票**
- 抬头：`仙桃市酒店烹饪协会`

### 2. 验证项目

1. **抬头验证**：自动识别发票类型并验证抬头信息
2. **纳税人识别号验证**：验证企业发票的纳税人识别号
3. **保单号验证**：检查发票备注中是否包含正确的保单号
4. **价税合计验证**：验证发票金额与保险费是否一致

### 3. 支持的发票格式

**XML发票**
- 自动解析XML结构
- 提取发票号码、买方信息、价税合计、备注等
- 支持标准电子发票XML格式

**PDF发票**
- 提取PDF文本内容
- 使用正则表达式匹配关键信息
- 支持标准电子发票PDF格式

## 使用方法

### 1. CSV文件准备

**重要更新**: 发票URL现在通过API自动查询，CSV文件无需包含xmlUrl和pdfUrl字段：

```csv
数据集名称,planCode,fixPremium,effectTime,expiryTime,destination,remark,tradeSerialNo
测试产品,00200007601,1.60,2025-07-15,2025-07-16,杭州,测试,TC1944585862039027713
```

### 1.1 发票URL自动查询

系统会根据`tradeSerialNo`自动查询发票URL：

**API接口**: `https://back.n.nkomol.cn/api/execute-sql`

**查询逻辑**:
- 使用交易流水号查询callbacks表
- 获取callback_type_raw=5的记录
- 提取invoice_url（PDF发票）和xml_url（XML发票）

**SQL查询语句**:
```sql
SELECT trade_serial_no, invoice_url, xml_url
FROM callbacks
WHERE callback_type_raw=5 AND trade_serial_no='交易流水号'
```

### 2. 命令行使用

```bash
# 只验证发票（使用API查询发票URL）
python pdf_validator.py test_api_invoice_data.csv invoice

# 同时验证保单和发票（使用API查询发票URL）
python pdf_validator.py test_api_invoice_data.csv both

# 只验证保单（默认）
python pdf_validator.py your_data.csv policy
```

**注意**: 发票验证需要确保：
1. CSV文件包含有效的`tradeSerialNo`字段
2. 网络连接正常，可以访问API接口
3. 交易流水号在数据库中存在对应的发票记录

### 3. 验证结果

程序会生成相应的验证报告：
- `validation_report_invoice.txt` - 发票验证报告
- `validation_report_both.txt` - 综合验证报告

## 示例输出

### XML发票验证结果示例
```
XML发票验证:
✅ 企业发票验证通过: 北京十斤文化传媒有限公司 (91110101MA01JDE656)
✅ 保单号验证通过
✅ 价税合计验证通过: 11.20元
```

### PDF发票验证结果示例
```
PDF发票验证:
✅ PDF发票抬头验证通过
✅ 保单号验证通过
✅ 价税合计验证通过: 1.60元
```

## 测试和演示

### 运行功能测试
```bash
python test_invoice_validation.py
```

### 运行演示脚本
```bash
python demo_invoice_validation.py
```

### 测试API查询功能
```bash
python test_invoice_api.py
```

## 技术实现

### 主要新增方法

1. **发票查询和下载方法**
   - `query_invoice_urls()` - 通过API查询发票URL
   - `download_invoice_xml()` - 下载XML发票
   - `download_invoice_pdf()` - 下载PDF发票

2. **发票解析方法**
   - `parse_xml_invoice()` - 解析XML发票内容
   - `extract_invoice_pdf_info()` - 提取PDF发票文本

3. **发票验证方法**
   - `validate_invoice()` - 验证发票信息
   - `is_personal_invoice()` - 判断个人发票
   - `is_enterprise_invoice()` - 判断企业发票
   - `is_government_invoice()` - 判断政府发票

4. **处理流程方法**
   - `process_invoice()` - 处理发票验证
   - 修改 `process_policy()` 支持验证模式
   - 修改 `process_all_policies()` 支持验证模式

## 错误处理

程序包含完善的错误处理：
- 网络下载失败处理
- XML解析错误处理
- PDF文本提取失败处理
- 数据格式验证错误处理
- 详细的错误日志记录

## 注意事项

1. **重要**: CSV文件不再需要xmlUrl和pdfUrl字段，系统会自动通过API查询
2. 确保CSV文件中的tradeSerialNo字段有效且存在于数据库中
3. 网络连接正常，能够访问API接口和发票下载链接
4. 发票文件格式符合标准电子发票规范
5. 验证规则可根据实际业务需求调整
6. API查询失败时会显示相应的错误信息

## 扩展性

发票验证功能设计具有良好的扩展性：
- 可以轻松添加新的发票类型识别规则
- 可以扩展验证项目
- 支持自定义验证逻辑
- 可以适配不同的发票格式
