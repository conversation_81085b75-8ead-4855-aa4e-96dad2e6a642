#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试PDF发票类型识别功能
"""

from pdf_validator import PDFValidator

def test_pdf_invoice_type_identification():
    """测试PDF发票类型识别"""
    print("测试PDF发票类型识别功能")
    print("=" * 60)
    
    validator = PDFValidator('test.csv')
    
    # 测试个人发票
    print("1. 个人发票测试:")
    print("-" * 40)
    
    personal_pdf_text = '''
    电子发票（普通发票） 发票号码：25997000000182333583 开票日期：2025年07月14日 
    购 销 
    买 售 
    名称：产品测试 名称：中国太平洋保险（集团）股份有限公司 
    方 方 
    信 统一社会信用代码/纳税人识别号： 信 统一社会信用代码/纳税人识别号：91310000132211707B 
    息 息 
    项目名称 规格型号 单 位 数 量 单 价 金 额 税率/征收率 税 额 
    *橡胶制品*旅行人身意外 ** 单 1 1.51 1.51 6% 0.09 
    伤害保险（2023版互联网 ） 
    合 计 ¥1.51 ¥0.09 
    价税合计（大写） 壹圆陆角 （小写）¥1.60 
    保单号:ASHH08014X25FN000BK5 
    备 注 
    开票人:太保财险
    '''
    
    result = validator.identify_pdf_invoice_type(personal_pdf_text)
    print(f"识别结果: {result}")
    print(f"发票类型: {result['type']}")
    print(f"买方名称: {result['buyer_name']}")
    print(f"纳税人识别号: {result['buyer_id'] if result['buyer_id'] else '无'}")
    print(f"验证状态: {'✅ 通过' if result['is_valid'] else '❌ 失败'}")
    
    # 测试企业发票
    print("\n2. 企业发票测试:")
    print("-" * 40)
    
    enterprise_pdf_text = '''
    电子发票（普通发票） 发票号码：25997000000182333584 开票日期：2025年07月14日 
    购 销 
    买 售 
    名称：中国太平洋保险（集团）股份有限公司虚拟单位 名称：中国太平洋保险（集团）股份有限公司 
    方 方 
    信 统一社会信用代码/纳税人识别号：919900000000214267 信 统一社会信用代码/纳税人识别号：91310000132211707B 
    息 息 
    项目名称 规格型号 单 位 数 量 单 价 金 额 税率/征收率 税 额 
    *保险服务*旅行人身意外 ** 单 1 12.26 12.26 6% 0.74 
    伤害保险（2023版互联网 ） 
    合 计 ¥12.26 ¥0.74 
    价税合计（大写） 壹拾叁圆 （小写）¥13.00 
    保单号:ASHH08014X25FN000BK6 
    备 注 
    开票人:太保财险
    '''
    
    result = validator.identify_pdf_invoice_type(enterprise_pdf_text)
    print(f"识别结果: {result}")
    print(f"发票类型: {result['type']}")
    print(f"买方名称: {result['buyer_name']}")
    print(f"纳税人识别号: {result['buyer_id'] if result['buyer_id'] else '无'}")
    print(f"验证状态: {'✅ 通过' if result['is_valid'] else '❌ 失败'}")
    
    # 测试另一个企业发票
    print("\n3. 另一个企业发票测试:")
    print("-" * 40)
    
    enterprise_pdf_text2 = '''
    电子发票（普通发票） 发票号码：25997000000182333585 开票日期：2025年07月14日 
    购 销 
    买 售 
    名称：北京十斤文化传媒有限公司 名称：中国太平洋保险（集团）股份有限公司 
    方 方 
    信 统一社会信用代码/纳税人识别号：91110101MA01JDE656 信 统一社会信用代码/纳税人识别号：91310000132211707B 
    息 息 
    项目名称 规格型号 单 位 数 量 单 价 金 额 税率/征收率 税 额 
    *保险服务*旅行人身意外 ** 单 1 11.79 11.79 6% 0.71 
    伤害保险（2023版互联网 ） 
    合 计 ¥11.79 ¥0.71 
    价税合计（大写） 壹拾贰圆伍角 （小写）¥12.50 
    保单号:ASHH08014X25FN000BK7 
    备 注 
    开票人:太保财险
    '''
    
    result = validator.identify_pdf_invoice_type(enterprise_pdf_text2)
    print(f"识别结果: {result}")
    print(f"发票类型: {result['type']}")
    print(f"买方名称: {result['buyer_name']}")
    print(f"纳税人识别号: {result['buyer_id'] if result['buyer_id'] else '无'}")
    print(f"验证状态: {'✅ 通过' if result['is_valid'] else '❌ 失败'}")
    
    # 测试政府发票
    print("\n4. 政府发票测试:")
    print("-" * 40)
    
    government_pdf_text = '''
    电子发票（普通发票） 发票号码：25997000000182333586 开票日期：2025年07月14日 
    购 销 
    买 售 
    名称：仙桃市酒店烹饪协会 名称：中国太平洋保险（集团）股份有限公司 
    方 方 
    信 统一社会信用代码/纳税人识别号： 信 统一社会信用代码/纳税人识别号：91310000132211707B 
    息 息 
    项目名称 规格型号 单 位 数 量 单 价 金 额 税率/征收率 税 额 
    *保险服务*旅行人身意外 ** 单 1 23.58 23.58 6% 1.42 
    伤害保险（2023版互联网 ） 
    合 计 ¥23.58 ¥1.42 
    价税合计（大写） 贰拾伍圆 （小写）¥25.00 
    保单号:ASHH08014X25FN000BK8 
    备 注 
    开票人:太保财险
    '''
    
    result = validator.identify_pdf_invoice_type(government_pdf_text)
    print(f"识别结果: {result}")
    print(f"发票类型: {result['type']}")
    print(f"买方名称: {result['buyer_name']}")
    print(f"纳税人识别号: {result['buyer_id'] if result['buyer_id'] else '无'}")
    print(f"验证状态: {'✅ 通过' if result['is_valid'] else '❌ 失败'}")

def test_pdf_invoice_validation():
    """测试完整的PDF发票验证"""
    print("\n" + "=" * 60)
    print("测试完整的PDF发票验证功能")
    print("=" * 60)
    
    validator = PDFValidator('test.csv')
    
    # 模拟保单信息
    policy_info = {
        'queryData': {
            'policyNo': 'ASHH08014X25FN000BK7'
        }
    }
    
    # 模拟CSV行数据
    csv_row = {
        'fixPremium': '12.50'
    }
    
    # 使用企业发票文本进行测试
    enterprise_pdf_text = '''
    电子发票（普通发票） 发票号码：25997000000182333585 开票日期：2025年07月14日 
    购 销 
    买 售 
    名称：北京十斤文化传媒有限公司 名称：中国太平洋保险（集团）股份有限公司 
    方 方 
    信 统一社会信用代码/纳税人识别号：91110101MA01JDE656 信 统一社会信用代码/纳税人识别号：91310000132211707B 
    息 息 
    项目名称 规格型号 单 位 数 量 单 价 金 额 税率/征收率 税 额 
    *保险服务*旅行人身意外 ** 单 1 11.79 11.79 6% 0.71 
    伤害保险（2023版互联网 ） 
    合 计 ¥11.79 ¥0.71 
    价税合计（大写） 壹拾贰圆伍角 （小写）¥12.50 
    保单号:ASHH08014X25FN000BK7 
    备 注 
    开票人:太保财险
    '''
    
    try:
        # 测试PDF发票验证
        validation_result = validator.validate_invoice(enterprise_pdf_text, policy_info, csv_row, "pdf")
        print("PDF发票验证结果:")
        print(validation_result)
        
        print("\n✅ PDF发票验证测试完成")
        
    except Exception as e:
        print(f"❌ PDF发票验证测试失败: {str(e)}")

if __name__ == "__main__":
    test_pdf_invoice_type_identification()
    test_pdf_invoice_validation()
