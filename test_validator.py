#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF验证器测试脚本
"""

import os
import sys
from pdf_validator import PDFValidator

def test_csv_reading():
    """测试CSV文件读取功能"""
    print("测试CSV文件读取...")
    
    csv_files = ['product.csv', '团险投保3款产品.csv']
    
    for csv_file in csv_files:
        if os.path.exists(csv_file):
            print(f"\n测试文件: {csv_file}")
            try:
                validator = PDFValidator(csv_file)
                data = validator.read_csv_data()
                print(f"读取到 {len(data)} 条记录")
                
                # 显示前几条记录
                for i, row in enumerate(data[:3]):
                    print(f"记录 {i+1}: {row}")
                    
            except Exception as e:
                print(f"读取失败: {str(e)}")
        else:
            print(f"文件不存在: {csv_file}")

def test_policy_query():
    """测试保单查询功能"""
    print("\n测试保单查询功能...")
    
    try:
        validator = PDFValidator('product.csv')
        result = validator.query_policy_info('test_serial_no')
        print("查询结果:")
        print(f"响应码: {result['respCode']}")
        print(f"响应消息: {result['respMsg']}")
        if 'queryData' in result:
            query_data = result['queryData']
            print(f"保单号: {query_data.get('policyNo')}")
            print(f"下载URL: {query_data.get('policyDownloadUrl')}")
            
    except Exception as e:
        print(f"查询失败: {str(e)}")

def test_directory_creation():
    """测试目录创建功能"""
    print("\n测试目录创建功能...")
    
    try:
        validator = PDFValidator('product.csv')
        print(f"下载目录: {validator.download_dir}")
        print(f"目录是否存在: {os.path.exists(validator.download_dir)}")
        
    except Exception as e:
        print(f"目录创建失败: {str(e)}")

def main():
    """主测试函数"""
    print("PDF验证器功能测试")
    print("=" * 50)
    
    # 检查依赖
    try:
        import requests
        import pdfplumber
        print("✅ 依赖库检查通过")
    except ImportError as e:
        print(f"❌ 缺少依赖库: {e}")
        return
    
    # 运行测试
    test_csv_reading()
    test_policy_query()
    test_directory_creation()
    
    print("\n测试完成!")

if __name__ == "__main__":
    main()
