import requests
import pdfplumber
import io
import re
import csv
import os
from datetime import datetime

class PDFValidator:
    def __init__(self, csv_path):
        self.csv_path = csv_path
        self.download_dir = os.path.join(os.path.dirname(csv_path), 'downloaded_pdfs')
        os.makedirs(self.download_dir, exist_ok=True)
        
    def read_csv_data(self):
        """读取CSV文件数据并返回包含tradeSerialNo的记录"""
        csv_data = []
        with open(self.csv_path, mode='r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            for row in reader:
                if row.get('tradeSerialNo'):
                    csv_data.append(row)
        return csv_data
    
    def query_policy_info(self, trade_serial_no):
        """模拟查询函数，实际使用时替换为真实API调用"""
        # 这里使用用户提供的示例数据作为模拟返回
        return {
            "respCode": "0",
            "respMsg": "请求成功",
            "queryData": {
                "code": "2",
                "message": "投保成功",
                "accountTime": "2025-07-14 10:32:47",
                "serialNo": "db18ca8c-d50d-4faf-a236-9848935eeb8f",
                "tradeSerialNo": trade_serial_no,
                "policyNo": "ASHH08014X25FN000BK5",
                "policyDownloadUrl": "https://api-uat.itcis.cn/api/guest/download/policy/hzMq_cFe5ubjlteYxzGareT_-sGoEbsTa8dqD5FpQygJ-52sblYvNMm9hXipgOrJg2xk8Lauq3qb29slwzugJ31iRurJsYsZSjclDCkcsSE",
                "sceneCode": "***********",
                "supplierCode": "913100007362379322",
                "parentCompany": "",
                "effectTime": "2025-07-15 00:00:00",
                "expiryTime": "2025-07-16 00:00:00"
            }
        }
    
    def download_pdf(self, policy_no, download_url):
        """下载PDF文件并按保单号命名保存"""
        pdf_path = os.path.join(self.download_dir, f"{policy_no}.pdf")
        
        # 如果文件已存在，直接返回路径
        if os.path.exists(pdf_path):
            return pdf_path
        
        # 下载PDF文件
        try:
            response = requests.get(download_url, stream=True)
            response.raise_for_status()
            
            with open(pdf_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            return pdf_path
        except Exception as e:
            raise Exception(f"下载PDF失败: {str(e)}")
    
    def extract_pdf_info(self, pdf_path):
        """提取PDF前3页文本信息"""
        page_texts = []
        try:
            with pdfplumber.open(pdf_path) as pdf:
                # 只处理前4页
                for page_number in range(min(4, len(pdf.pages))):
                    page = pdf.pages[page_number]
                    text = page.extract_text()
                    if text:
                        page_texts.append(text)
                    else:
                        page_texts.append("[无法提取文本，可能是扫描件]")
            return '\n--- 分页 ---\n'.join(page_texts)
        except Exception as e:
            raise Exception(f"提取PDF信息失败: {str(e)}")
    
    def validate_policy(self, pdf_text, policy_info, csv_row):
        """验证PDF内容与保单信息是否一致"""
        validation_results = []
        
        # 1. 验证保单号
        policy_no = policy_info['queryData']['policyNo']
        if re.search(policy_no, pdf_text):
            validation_results.append("✅ 保单号验证通过")
        else:
            validation_results.append(f"❌ 保单号验证失败: PDF中未找到 {policy_no}")
        
        # 2. 验证投保人名称
        if re.search(r'投保人名称：\s*产品测试', pdf_text):
            validation_results.append("✅ 投保人名称验证通过")
        else:
            validation_results.append("❌ 投保人名称验证失败")
        
        # 3. 验证投保人证件信息
        if re.search(r'投保人证件类型：\s*身份证', pdf_text) and re.search(r'530102194410193109', pdf_text):
            validation_results.append("✅ 投保人证件信息验证通过")
        else:
            validation_results.append("❌ 投保人证件信息验证失败")
        
        # 4. 验证旅游目的地
        destination = csv_row['destination']
        # 处理CSV中的引号和空格
        destination = destination.replace('"','').strip()
        
        # 5. 验证保险费
        fix_premium = csv_row['fixPremium']
        # 构建保费的正则表达式模式，匹配"X元X角整（RMB: X.XX元）"
        premium_pattern = re.compile(rf'保险费：\\s*人民币.*?（RMB\\s*:\\s*{float(fix_premium):.2f}元）')
        if premium_pattern.search(pdf_text):
            validation_results.append(f"✅ 保险费验证通过: {fix_premium}元")
        else:
            validation_results.append(f"❌ 保险费验证失败: PDF中未找到 {fix_premium}元")
        
        # 6. 验证保险期间
        effect_time = policy_info['queryData']['effectTime'].split()[0]
        expiry_time = policy_info['queryData']['expiryTime'].split()[0]
        # 转换日期格式为PDF中的格式（YYYY年MM月DD日）
        effect_time_formatted = datetime.strptime(effect_time, '%Y-%m-%d').strftime('%Y年%m月%d日')
        expiry_time_formatted = datetime.strptime(expiry_time, '%Y-%m-%d').strftime('%Y年%m月%d日')
        
        period_pattern = re.compile(rf'保险期间：\\s*自（From）{effect_time_formatted}.*?至（To）{expiry_time_formatted}')
        if period_pattern.search(pdf_text):
            validation_results.append(f"✅ 保险期间验证通过: {effect_time_formatted} 至 {expiry_time_formatted}")
        else:
            validation_results.append(f"❌ 保险期间验证失败: 预期 {effect_time_formatted} 至 {expiry_time_formatted}")
        
        # 7. 验证销售机构
        if re.search(r'委托销售机构/营销员名称：\\s*泰昌保险销售有限公司', pdf_text):
            validation_results.append("✅ 销售机构验证通过")
        else:
            validation_results.append("❌ 销售机构验证失败")

        return '\n'.join(validation_results)

    def process_policy(self, csv_row):
        """处理单个保单"""
        policy_no = csv_row['policyNo']
        download_url = self.get_policy_download_url(policy_no)