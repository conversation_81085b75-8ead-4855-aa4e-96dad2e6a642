import requests
import pdfplumber
import re
import csv
import os
import sys
from datetime import datetime
import json
import base64
import hashlib
import xml.etree.ElementTree as ET


class PDFValidator:
    def __init__(self, csv_path):
        self.csv_path = csv_path
        self.download_dir = os.path.join(os.path.dirname(csv_path), 'downloaded_pdfs')
        os.makedirs(self.download_dir, exist_ok=True)
        
    def read_csv_data(self):
        """读取CSV文件数据"""
        csv_data = []
        try:
            with open(self.csv_path, mode='r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                for row in reader:
                    # 过滤掉空行
                    if any(value.strip() for value in row.values() if value):
                        csv_data.append(row)
            print(f"从CSV文件读取到 {len(csv_data)} 条记录")
            return csv_data
        except Exception as e:
            raise Exception(f"读取CSV文件失败: {str(e)}")
    
    def query_policy_info(self, trade_serial_no):
        """查询保单信息"""

        
        url = "https://api-uat.itcis.cn/api/scene/query"
        
        # 准备请求数据
        request_data = {
            "tradeSerialNo": trade_serial_no,
            "serialNo": ""
        }
        
        # 1. 将请求数据转换为标准化的JSON字符串
        normalized_json = json.dumps(request_data, separators=(',', ':'))
        
        # 2. Base64编码
        base64_data = base64.b64encode(normalized_json.encode('utf-8')).decode('utf-8')
        
        # 3. 计算签名
        key = "9d17b3889124a7c16a4c1b9d3d9a6a33"
        sign_data = base64_data + key
        sign = hashlib.md5(sign_data.encode('utf-8')).hexdigest().lower()
        
        # 4. 准备表单数据
        form_data = {
            'data': base64_data,
            'partnerId': 'sale',
            'sign': sign
        }
        
        # 5. 发送请求
        headers = {'Content-Type': 'application/x-www-form-urlencoded'}
        try:
            response = requests.post(url, data=form_data, headers=headers)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            raise Exception(f"查询保单信息失败: {str(e)}")
    
    def download_pdf(self, policy_no, download_url):
        """下载PDF文件并按保单号命名保存"""
        # 清理文件名中的非法字符
        safe_policy_no = re.sub(r'[<>:"/\\|?*]', '_', policy_no)
        pdf_path = os.path.join(self.download_dir, f"{safe_policy_no}.pdf")

        # 如果文件已存在，直接返回路径
        if os.path.exists(pdf_path):
            print(f"PDF文件已存在: {pdf_path}")
            return pdf_path

        # 下载PDF文件
        try:
            print(f"正在下载PDF: {download_url}")
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            response = requests.get(download_url, stream=True, headers=headers, timeout=30)
            response.raise_for_status()

            # 检查响应内容类型
            content_type = response.headers.get('content-type', '').lower()
            if 'pdf' not in content_type and 'application/octet-stream' not in content_type:
                print(f"警告: 响应内容类型不是PDF: {content_type}")

            with open(pdf_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)

            # 验证下载的文件大小
            file_size = os.path.getsize(pdf_path)
            if file_size == 0:
                os.remove(pdf_path)
                raise Exception("下载的PDF文件为空")

            print(f"PDF下载成功: {pdf_path} (大小: {file_size} 字节)")
            return pdf_path

        except requests.exceptions.RequestException as e:
            raise Exception(f"下载PDF失败 - 网络错误: {str(e)}")
        except Exception as e:
            if os.path.exists(pdf_path):
                os.remove(pdf_path)
            raise Exception(f"下载PDF失败: {str(e)}")
    
    def extract_pdf_info(self, pdf_path):
        """提取PDF前4页文本信息"""
        page_texts = []
        try:
            print(f"正在提取PDF文本: {pdf_path}")
            with pdfplumber.open(pdf_path) as pdf:
                total_pages = len(pdf.pages)
                print(f"PDF总页数: {total_pages}")

                # 只处理前4页
                pages_to_process = min(4, total_pages)
                for page_number in range(pages_to_process):
                    try:
                        page = pdf.pages[page_number]
                        text = page.extract_text()
                        if text and text.strip():
                            # 清理文本，移除多余的空白字符
                            cleaned_text = re.sub(r'\s+', ' ', text).strip()
                            page_texts.append(f"--- 第 {page_number + 1} 页 ---\n{cleaned_text}")
                        else:
                            page_texts.append(f"--- 第 {page_number + 1} 页 ---\n[无法提取文本，可能是扫描件]")
                    except Exception as page_error:
                        page_texts.append(f"--- 第 {page_number + 1} 页 ---\n[提取失败: {str(page_error)}]")

                if not page_texts:
                    raise Exception("无法从PDF中提取任何文本内容")

                full_text = '\n\n'.join(page_texts)
                print(f"成功提取PDF文本，总长度: {len(full_text)} 字符")
                return full_text

        except Exception as e:
            raise Exception(f"提取PDF信息失败: {str(e)}")
    
    def validate_policy(self, pdf_text, policy_info, csv_row):
        """验证PDF内容与保单信息是否一致，支持境内险和境外险格式"""
        validation_results = []

        # 1. 验证保单号
        policy_no = policy_info['queryData']['policyNo']
        if re.search(policy_no, pdf_text):
            validation_results.append("✅ 保单号验证通过")
        else:
            validation_results.append(f"❌ 保单号验证失败: PDF中未找到 {policy_no}")

        # 2. 验证投保人名称（支持境内险和境外险格式）
        name_patterns = [
            r'投保人名称：\s*产品测试',  # 境内险格式
            r'投保人名称\s+Applicant：\s*产品测试',  # 境外险格式
            r'Applicant：\s*产品测试'  # 简化境外险格式
        ]
        name_found = False
        for pattern in name_patterns:
            if re.search(pattern, pdf_text):
                name_found = True
                break

        if name_found:
            validation_results.append("✅ 投保人名称验证通过")
        else:
            validation_results.append("❌ 投保人名称验证失败")

        # 3. 验证投保人证件信息（支持境内险和境外险格式）
        id_type_patterns = [
            r'投保人证件类型：\s*身份证',  # 境内险格式
            r'投保人证件类型\s+ID Type of the Applicant：\s*身份证\s+National ID',  # 境外险格式
            r'ID Type of the Applicant：\s*身份证\s+National ID'  # 简化境外险格式
        ]
        id_no_patterns = [
            r'投保人证件号码：\s*530102194410193109',  # 境内险格式
            r'投保人证件号码\s+ID No：\s*530102194410193109',  # 境外险格式
            r'ID No：\s*530102194410193109'  # 简化境外险格式
        ]

        id_type_found = any(re.search(pattern, pdf_text) for pattern in id_type_patterns)
        id_no_found = any(re.search(pattern, pdf_text) for pattern in id_no_patterns)

        if id_type_found and id_no_found:
            validation_results.append("✅ 投保人证件信息验证通过")
        else:
            validation_results.append("❌ 投保人证件信息验证失败")
        
        # 4. 验证旅游目的地
        destination = csv_row['destination']
        # 处理CSV中的引号和空格
        destination = destination.replace('"','').strip()
        
        # 4. 验证旅游目的地
        destination = csv_row.get('destination', '')
        # 处理CSV中的引号和空格
        destination = destination.replace('"','').strip()
        if destination:
            # 分割多个目的地
            destinations = [d.strip() for d in destination.split(',')]
            found_destinations = []
            for dest in destinations:
                if dest and re.search(re.escape(dest), pdf_text):
                    found_destinations.append(dest)

            if found_destinations:
                validation_results.append(f"✅ 旅游目的地验证通过: {', '.join(found_destinations)}")
            else:
                validation_results.append(f"❌ 旅游目的地验证失败: PDF中未找到 {destination}")

        # 5. 验证保险费（支持境内险和境外险格式）
        fix_premium = csv_row.get('fixPremium', '')
        if fix_premium:
            try:
                premium_value = float(fix_premium)

                # 境内险格式：保险费：人民币X元X角整（RMB: X.XX元）
                premium_pattern_domestic = re.compile(rf'保险费：\s*人民币.*?（RMB\s*:\s*{premium_value:.2f}元）')

                # 境外险格式：保险费 Total Premium： 人民币 柒元伍角整（RMB: 7.50元）
                premium_pattern_overseas = re.compile(rf'保险费\s+Total Premium：\s*人民币.*?（RMB:\s*{premium_value:.2f}元）')

                # 简化境外险格式
                premium_pattern_overseas_simple = re.compile(rf'Total Premium：\s*人民币.*?（RMB:\s*{premium_value:.2f}元）')

                premium_patterns = [premium_pattern_domestic, premium_pattern_overseas, premium_pattern_overseas_simple]
                premium_found = any(pattern.search(pdf_text) for pattern in premium_patterns)

                if premium_found:
                    validation_results.append(f"✅ 保险费验证通过: {fix_premium}元")
                else:
                    validation_results.append(f"❌ 保险费验证失败: PDF中未找到 {fix_premium}元")
            except ValueError:
                validation_results.append(f"❌ 保险费格式错误: {fix_premium}")

        # 6. 验证保险期间（支持境内险和境外险格式）
        try:
            effect_time = policy_info['queryData']['effectTime'].split()[0]
            expiry_time = policy_info['queryData']['expiryTime'].split()[0]

            # 转换日期格式为PDF中的格式（YYYY年MM月DD日）
            effect_time_formatted = datetime.strptime(effect_time, '%Y-%m-%d').strftime('%Y年%m月%d日')
            expiry_time_formatted = datetime.strptime(expiry_time, '%Y-%m-%d').strftime('%Y年%m月%d日')

            # 转换为境外险格式（YYYY-MM-DD）
            effect_time_overseas = effect_time
            expiry_time_overseas = expiry_time

            # 境内险格式：保险期间：自（From）2025年07月15日 至（To）2025年07月16日
            period_pattern_domestic = re.compile(rf'保险期间：\s*自（From）{re.escape(effect_time_formatted)}.*?至（To）{re.escape(expiry_time_formatted)}')

            # 境外险格式：保险期间 Insured Period： 自（From）2025-07-15 00:00起至（To）2025-07-16 00:00止
            period_pattern_overseas = re.compile(rf'保险期间\s+Insured Period：\s*自（From）{re.escape(effect_time_overseas)}\s+00:00起至（To）{re.escape(expiry_time_overseas)}\s+00:00止')

            # 简化境外险格式
            period_pattern_overseas_simple = re.compile(rf'Insured Period：\s*自（From）{re.escape(effect_time_overseas)}\s+00:00起至（To）{re.escape(expiry_time_overseas)}\s+00:00止')

            period_patterns = [period_pattern_domestic, period_pattern_overseas, period_pattern_overseas_simple]
            period_found = any(pattern.search(pdf_text) for pattern in period_patterns)

            if period_found:
                validation_results.append(f"✅ 保险期间验证通过: {effect_time} 至 {expiry_time}")
            else:
                validation_results.append(f"❌ 保险期间验证失败: 预期 {effect_time} 至 {expiry_time}")
        except Exception as e:
            validation_results.append(f"❌ 保险期间验证失败: 日期格式错误 - {str(e)}")

        # 7. 验证销售机构（支持境内险和境外险格式）
        agent_patterns = [
            r'委托销售机构/营销员名称：\s*泰昌保险销售有限公司',  # 境内险格式
            r'委托销售机构/营销员名称\s+Agent：\s*泰昌保险销售有限公司',  # 境外险格式
            r'Agent：\s*泰昌保险销售有限公司'  # 简化境外险格式
        ]

        agent_found = any(re.search(pattern, pdf_text) for pattern in agent_patterns)

        if agent_found:
            validation_results.append("✅ 销售机构验证通过")
        else:
            validation_results.append("❌ 销售机构验证失败")

        return '\n'.join(validation_results)

    def download_invoice_xml(self, xml_url, filename_prefix="invoice"):
        """下载XML发票文件"""
        try:
            print(f"正在下载XML发票: {xml_url}")
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            response = requests.get(xml_url, headers=headers, timeout=30)
            response.raise_for_status()

            # 保存XML文件
            xml_path = os.path.join(self.download_dir, f"{filename_prefix}.xml")
            with open(xml_path, 'w', encoding='utf-8') as f:
                f.write(response.text)

            print(f"XML发票下载成功: {xml_path}")
            return xml_path, response.text

        except Exception as e:
            raise Exception(f"下载XML发票失败: {str(e)}")

    def download_invoice_pdf(self, pdf_url, filename_prefix="invoice"):
        """下载PDF发票文件"""
        try:
            print(f"正在下载PDF发票: {pdf_url}")
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            response = requests.get(pdf_url, stream=True, headers=headers, timeout=30)
            response.raise_for_status()

            # 保存PDF文件
            pdf_path = os.path.join(self.download_dir, f"{filename_prefix}_invoice.pdf")
            with open(pdf_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)

            print(f"PDF发票下载成功: {pdf_path}")
            return pdf_path

        except Exception as e:
            raise Exception(f"下载PDF发票失败: {str(e)}")

    def parse_xml_invoice(self, xml_content):
        """解析XML发票内容"""
        try:
            root = ET.fromstring(xml_content)

            # 提取发票基本信息
            invoice_info = {}

            # 发票号码
            invoice_number = root.find('.//InvoiceNumber')
            if invoice_number is not None:
                invoice_info['invoice_number'] = invoice_number.text

            # 销售方信息
            seller_name = root.find('.//SellerName')
            if seller_name is not None:
                invoice_info['seller_name'] = seller_name.text

            seller_id = root.find('.//SellerIdNum')
            if seller_id is not None:
                invoice_info['seller_id'] = seller_id.text

            # 购买方信息
            buyer_name = root.find('.//BuyerName')
            if buyer_name is not None:
                invoice_info['buyer_name'] = buyer_name.text

            buyer_id = root.find('.//BuyerIdNum')
            if buyer_id is not None:
                invoice_info['buyer_id'] = buyer_id.text

            # 价税合计
            total_amount = root.find('.//TotalTax-includedAmount')
            if total_amount is not None:
                invoice_info['total_amount'] = total_amount.text

            # 备注中的保单号
            remark = root.find('.//Remark')
            if remark is not None:
                invoice_info['remark'] = remark.text

            return invoice_info

        except Exception as e:
            raise Exception(f"解析XML发票失败: {str(e)}")

    def extract_invoice_pdf_info(self, pdf_path):
        """提取PDF发票文本信息"""
        try:
            print(f"正在提取PDF发票文本: {pdf_path}")
            with pdfplumber.open(pdf_path) as pdf:
                # 只处理第一页
                if len(pdf.pages) > 0:
                    page = pdf.pages[0]
                    text = page.extract_text()
                    if text and text.strip():
                        print(f"成功提取PDF发票文本，长度: {len(text)} 字符")
                        return text
                    else:
                        raise Exception("无法从PDF发票中提取文本内容")
                else:
                    raise Exception("PDF发票文件为空")

        except Exception as e:
            raise Exception(f"提取PDF发票信息失败: {str(e)}")

    def get_policy_download_url(self, trade_serial_no):
        """获取保单下载URL"""
        try:
            policy_info = self.query_policy_info(trade_serial_no)
            if policy_info['respCode'] == '0' and policy_info['queryData']['code'] == '2':
                return policy_info['queryData']['policyDownloadUrl'], policy_info
            else:
                raise Exception(f"查询保单信息失败: {policy_info.get('respMsg', '未知错误')}")
        except Exception as e:
            raise Exception(f"获取保单下载URL失败: {str(e)}")

    def validate_invoice(self, invoice_data, policy_info, csv_row, invoice_type="xml"):
        """验证发票信息"""
        validation_results = []

        # 获取保单号用于验证
        policy_no = policy_info['queryData']['policyNo']
        fix_premium = csv_row.get('fixPremium', '')

        if invoice_type == "xml":
            # XML发票验证
            # 1. 验证抬头和纳税人识别号
            buyer_name = invoice_data.get('buyer_name', '')
            buyer_id = invoice_data.get('buyer_id', '')

            # 判断发票类型
            if self.is_personal_invoice(buyer_name, buyer_id):
                validation_results.append(f"✅ 个人发票验证通过: {buyer_name}")
            elif self.is_enterprise_invoice(buyer_name, buyer_id):
                validation_results.append(f"✅ 企业发票验证通过: {buyer_name} ({buyer_id})")
            elif self.is_government_invoice(buyer_name, buyer_id):
                validation_results.append(f"✅ 政府发票验证通过: {buyer_name}")
            else:
                validation_results.append(f"❌ 发票抬头验证失败: 未知类型 {buyer_name}")

            # 2. 验证保单号
            remark = invoice_data.get('remark', '')
            if policy_no in remark:
                validation_results.append("✅ 保单号验证通过")
            else:
                validation_results.append(f"❌ 保单号验证失败: 发票备注中未找到 {policy_no}")

            # 3. 验证价税合计
            total_amount = invoice_data.get('total_amount', '')
            if fix_premium and total_amount:
                try:
                    expected_amount = float(fix_premium)
                    actual_amount = float(total_amount)
                    if abs(expected_amount - actual_amount) < 0.01:  # 允许0.01的误差
                        validation_results.append(f"✅ 价税合计验证通过: {total_amount}元")
                    else:
                        validation_results.append(f"❌ 价税合计验证失败: 预期 {expected_amount}元, 实际 {actual_amount}元")
                except ValueError:
                    validation_results.append(f"❌ 价税合计格式错误: {total_amount}")

        else:
            # PDF发票验证
            pdf_text = invoice_data  # 对于PDF，invoice_data就是提取的文本

            # 1. 验证抬头和纳税人识别号
            if self.validate_pdf_invoice_header(pdf_text):
                validation_results.append("✅ PDF发票抬头验证通过")
            else:
                validation_results.append("❌ PDF发票抬头验证失败")

            # 2. 验证保单号
            if policy_no in pdf_text:
                validation_results.append("✅ 保单号验证通过")
            else:
                validation_results.append(f"❌ 保单号验证失败: PDF发票中未找到 {policy_no}")

            # 3. 验证价税合计
            if fix_premium:
                try:
                    expected_amount = float(fix_premium)
                    # 匹配价税合计模式
                    amount_pattern = re.compile(rf'价税合计.*?¥{expected_amount:.2f}')
                    if amount_pattern.search(pdf_text):
                        validation_results.append(f"✅ 价税合计验证通过: {expected_amount}元")
                    else:
                        validation_results.append(f"❌ 价税合计验证失败: PDF中未找到 {expected_amount}元")
                except ValueError:
                    validation_results.append(f"❌ 价税合计格式错误: {fix_premium}")

        return '\n'.join(validation_results)

    def is_personal_invoice(self, buyer_name, buyer_id):
        """判断是否为个人发票"""
        personal_names = ["产品测试", "个人产品测试"]
        return buyer_name in personal_names and (not buyer_id or buyer_id.strip() == "")

    def is_enterprise_invoice(self, buyer_name, buyer_id):
        """判断是否为企业发票"""
        enterprise_info = {
            "中国太平洋保险（集团）股份有限公司虚拟单位": "919900000000214267",
            "北京十斤文化传媒有限公司": "91110101MA01JDE656"
        }
        return buyer_name in enterprise_info and buyer_id == enterprise_info.get(buyer_name)

    def is_government_invoice(self, buyer_name, buyer_id):
        """判断是否为政府发票"""
        government_names = ["仙桃市酒店烹饪协会"]
        return buyer_name in government_names

    def validate_pdf_invoice_header(self, pdf_text):
        """验证PDF发票抬头"""
        # 个人发票
        personal_patterns = [
            r'产品测试',
            r'个人产品测试'
        ]

        # 企业发票 - 修改正则表达式以适应PDF格式
        enterprise_patterns = [
            r'中国太平洋保险（集团）股份有限公司虚拟单位',
            r'919900000000214267',
            r'北京十斤文化传媒有限公司',
            r'91110101MA01JDE656'
        ]

        # 政府发票
        government_patterns = [
            r'仙桃市酒店烹饪协会'
        ]

        all_patterns = personal_patterns + enterprise_patterns + government_patterns

        # 检查是否匹配任何模式
        for pattern in all_patterns:
            if re.search(pattern, pdf_text):
                return True
        return False

    def process_invoice(self, csv_row, policy_info):
        """处理发票验证"""
        invoice_results = []

        # 检查是否有XML发票URL
        xml_url = csv_row.get('xmlUrl', '').strip()
        if xml_url:
            try:
                # 下载并解析XML发票
                xml_path, xml_content = self.download_invoice_xml(xml_url, f"invoice_{policy_info['queryData']['policyNo']}")
                invoice_data = self.parse_xml_invoice(xml_content)

                # 验证XML发票
                xml_validation = self.validate_invoice(invoice_data, policy_info, csv_row, "xml")
                invoice_results.append(f"XML发票验证:\n{xml_validation}")

            except Exception as e:
                invoice_results.append(f"❌ XML发票处理失败: {str(e)}")

        # 检查是否有PDF发票URL
        pdf_url = csv_row.get('pdfUrl', '').strip()
        if pdf_url:
            try:
                # 下载PDF发票
                pdf_path = self.download_invoice_pdf(pdf_url, f"invoice_{policy_info['queryData']['policyNo']}")

                # 提取PDF发票文本
                pdf_text = self.extract_invoice_pdf_info(pdf_path)

                # 验证PDF发票
                pdf_validation = self.validate_invoice(pdf_text, policy_info, csv_row, "pdf")
                invoice_results.append(f"PDF发票验证:\n{pdf_validation}")

            except Exception as e:
                invoice_results.append(f"❌ PDF发票处理失败: {str(e)}")

        return '\n\n'.join(invoice_results) if invoice_results else "未找到发票URL"

    def process_policy(self, csv_row, validation_mode="policy"):
        """处理单个保单
        validation_mode: 'policy' - 只验证保单, 'invoice' - 只验证发票, 'both' - 同时验证
        """
        trade_serial_no = csv_row.get('tradeSerialNo', '').strip()

        # 如果没有交易流水号，生成一个模拟的流水号用于测试
        if not trade_serial_no:
            # 使用planCode和fixPremium生成一个唯一标识
            plan_code = csv_row.get('planCode', 'unknown')
            fix_premium = csv_row.get('fixPremium', '0')
            trade_serial_no = f"test_{plan_code}_{fix_premium}"
            print(f"警告: 缺少交易流水号，使用模拟流水号: {trade_serial_no}")

        try:
            # 1. 获取保单信息和下载URL
            download_url, policy_info = self.get_policy_download_url(trade_serial_no)
            policy_no = policy_info['queryData']['policyNo']

            result = {
                'status': 'success',
                'trade_serial_no': trade_serial_no,
                'policy_no': policy_no,
                'csv_row': csv_row
            }

            # 根据验证模式处理不同内容
            if validation_mode in ['policy', 'both']:
                # 2. 下载PDF保单文件
                pdf_path = self.download_pdf(policy_no, download_url)
                result['pdf_path'] = pdf_path

                # 3. 提取PDF文本
                pdf_text = self.extract_pdf_info(pdf_path)

                # 4. 验证保单信息
                validation_result = self.validate_policy(pdf_text, policy_info, csv_row)
                result['policy_validation'] = validation_result

            if validation_mode in ['invoice', 'both']:
                # 5. 处理发票验证
                invoice_result = self.process_invoice(csv_row, policy_info)
                result['invoice_validation'] = invoice_result

            return result

        except Exception as e:
            return {
                'status': 'error',
                'trade_serial_no': trade_serial_no,
                'message': str(e),
                'csv_row': csv_row
            }

    def process_all_policies(self, validation_mode="policy"):
        """处理所有保单
        validation_mode: 'policy' - 只验证保单, 'invoice' - 只验证发票, 'both' - 同时验证
        """
        csv_data = self.read_csv_data()
        results = []

        print(f"开始处理 {len(csv_data)} 条记录，验证模式: {validation_mode}...")

        for i, csv_row in enumerate(csv_data, 1):
            print(f"\n处理第 {i}/{len(csv_data)} 条记录...")
            print(f"交易流水号: {csv_row.get('tradeSerialNo', 'N/A')}")

            # 如果 tradeSerialNo 为空，跳过处理
            if not csv_row.get('tradeSerialNo'):
                print("❌ 跳过处理，缺少交易流水号")
                results.append({
                    'status': 'skipped',
                    'trade_serial_no': 'N/A',
                    'message': '缺少交易流水号',
                    'csv_row': csv_row
                })
                continue

            result = self.process_policy(csv_row, validation_mode)
            results.append(result)

            if result['status'] == 'success':
                print("✅ 处理成功")
                print(f"保单号: {result['policy_no']}")

                if 'pdf_path' in result:
                    print(f"PDF文件: {result['pdf_path']}")

                if 'policy_validation' in result:
                    print("保单验证结果:")
                    print(result['policy_validation'])

                if 'invoice_validation' in result:
                    print("发票验证结果:")
                    print(result['invoice_validation'])
            else:
                print("❌ 处理失败")
                print(f"错误信息: {result['message']}")

        return results

    def generate_report(self, results, validation_mode="policy"):
        """生成处理报告"""
        report_name = f'validation_report_{validation_mode}.txt'
        report_path = os.path.join(os.path.dirname(self.csv_path), report_name)

        with open(report_path, 'w', encoding='utf-8') as f:
            if validation_mode == "policy":
                f.write("PDF保单验证报告\n")
            elif validation_mode == "invoice":
                f.write("发票验证报告\n")
            else:
                f.write("保单和发票综合验证报告\n")

            f.write("=" * 50 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"验证模式: {validation_mode}\n")
            f.write(f"总记录数: {len(results)}\n")

            success_count = sum(1 for r in results if r['status'] == 'success')
            error_count = len(results) - success_count

            f.write(f"成功处理: {success_count} 条\n")
            f.write(f"处理失败: {error_count} 条\n\n")

            for i, result in enumerate(results, 1):
                f.write(f"\n记录 {i}:\n")
                f.write("-" * 30 + "\n")

                if result['status'] == 'success':
                    f.write(f"状态: ✅ 成功\n")
                    f.write(f"交易流水号: {result['trade_serial_no']}\n")
                    f.write(f"保单号: {result['policy_no']}\n")

                    if 'pdf_path' in result:
                        f.write(f"PDF文件: {result['pdf_path']}\n")

                    if 'policy_validation' in result:
                        f.write("保单验证结果:\n")
                        f.write(result['policy_validation'] + "\n\n")

                    if 'invoice_validation' in result:
                        f.write("发票验证结果:\n")
                        f.write(result['invoice_validation'] + "\n\n")

                elif result['status'] == 'skipped':
                    continue
                else:
                    f.write(f"状态: ❌ 失败\n")
                    f.write(f"交易流水号: {result.get('trade_serial_no', 'N/A')}\n")
                    f.write(f"错误信息: {result['message']}\n")

                f.write(f"CSV数据: {result['csv_row']}\n")

        print(f"\n报告已生成: {report_path}")
        return report_path


def main():
    """主函数"""
    if len(sys.argv) < 2 or len(sys.argv) > 3:
        print("使用方法: python pdf_validator.py <csv_file_path> [validation_mode]")
        print("validation_mode 选项:")
        print("  policy  - 只验证保单信息 (默认)")
        print("  invoice - 只验证发票信息")
        print("  both    - 同时验证保单和发票信息")
        print("\n示例:")
        print("  python pdf_validator.py test_data.csv")
        print("  python pdf_validator.py test_data.csv policy")
        print("  python pdf_validator.py test_data.csv invoice")
        print("  python pdf_validator.py test_data.csv both")
        return

    csv_path = sys.argv[1]
    validation_mode = sys.argv[2] if len(sys.argv) == 3 else "policy"

    # 验证模式参数
    valid_modes = ["policy", "invoice", "both"]
    if validation_mode not in valid_modes:
        print(f"错误: 无效的验证模式 '{validation_mode}'")
        print(f"有效选项: {', '.join(valid_modes)}")
        return

    if not os.path.exists(csv_path):
        print(f"错误: CSV文件不存在 - {csv_path}")
        return

    try:
        print(f"开始处理CSV文件: {csv_path}")
        print(f"验证模式: {validation_mode}")

        # 创建验证器实例
        validator = PDFValidator(csv_path)

        # 处理所有保单
        results = validator.process_all_policies(validation_mode)

        # 生成报告
        report_path = validator.generate_report(results, validation_mode)

        print(f"\n处理完成!")
        print(f"报告文件: {report_path}")

        # 显示统计信息
        success_count = sum(1 for r in results if r['status'] == 'success')
        error_count = len(results) - success_count
        print(f"统计信息: 成功 {success_count} 条, 失败 {error_count} 条")

    except KeyboardInterrupt:
        print("\n用户中断程序执行")
    except Exception as e:
        print(f"程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()