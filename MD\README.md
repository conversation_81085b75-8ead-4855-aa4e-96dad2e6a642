# PDF保单和发票验证器

这是一个用于验证PDF保单信息和发票信息的Python工具，可以从CSV文件读取保单数据，下载对应的PDF文件和发票文件，并验证内容与保单信息的一致性。

## 功能特性

- 📄 从CSV文件读取保单数据
- 🔍 查询保单信息（API调用）
- ⬇️ 自动下载PDF保单文件
- 📝 提取PDF文本内容（前4页）
- ✅ 验证保单信息一致性
- 🧾 **新增：发票验证功能**
  - 支持XML发票和PDF发票验证
  - 验证发票抬头、纳税人识别号、保单号、价税合计
  - 自动识别个人发票、企业发票、政府发票
- 📊 生成详细的验证报告

## 安装依赖

```bash
pip install requests pdfplumber
```

## 使用方法

### 命令行使用

```bash
python pdf_validator.py <csv_file_path> [validation_mode]
```

**验证模式选项：**
- `policy` - 只验证保单信息（默认）
- `invoice` - 只验证发票信息
- `both` - 同时验证保单和发票信息

**示例：**
```bash
# 只验证保单
python pdf_validator.py test_data.csv
python pdf_validator.py test_data.csv policy

# 只验证发票
python pdf_validator.py test_invoice_data.csv invoice

# 同时验证保单和发票
python pdf_validator.py test_invoice_data.csv both
```

### CSV文件格式

CSV文件应包含以下字段：

**基础字段：**
- `数据集名称`: 产品名称
- `planCode`: 计划代码
- `fixPremium`: 保险费
- `effectTime`: 生效时间
- `expiryTime`: 到期时间
- `destination`: 旅游目的地
- `remark`: 备注
- `tradeSerialNo`: 交易流水号（可选，如果为空会自动生成）

**发票验证说明：**
- 发票URL通过API自动查询获取，无需在CSV中提供
- 系统会根据`tradeSerialNo`自动查询对应的XML和PDF发票地址

### 验证项目

程序会验证以下内容，**支持境内险和境外险两种格式**：

1. **保单号验证**: 检查PDF中是否包含正确的保单号
2. **投保人名称验证**: 验证投保人名称为"产品测试"
   - 境内险格式：`投保人名称：产品测试`
   - 境外险格式：`投保人名称 Applicant： 产品测试`
3. **投保人证件信息验证**: 验证身份证号码
   - 境内险格式：`投保人证件类型：身份证` + `投保人证件号码：530102194410193109`
   - 境外险格式：`投保人证件类型 ID Type of the Applicant： 身份证 National ID` + `投保人证件号码 ID No： 530102194410193109`
4. **旅游目的地验证**: 检查PDF中是否包含指定的目的地
5. **保险费验证**: 验证保险费金额
   - 境内险格式：`保险费：人民币X元X角整（RMB: X.XX元）`
   - 境外险格式：`保险费 Total Premium： 人民币 X元X角整（RMB: X.XX元）`
6. **保险期间验证**: 验证保险生效和到期时间
   - 境内险格式：`保险期间：自（From）2025年07月15日 至（To）2025年07月16日`
   - 境外险格式：`保险期间 Insured Period： 自（From）2025-07-15 00:00起至（To）2025-07-16 00:00止`
7. **销售机构验证**: 验证销售机构名称
   - 境内险格式：`委托销售机构/营销员名称：泰昌保险销售有限公司`
   - 境外险格式：`委托销售机构/营销员名称 Agent：泰昌保险销售有限公司`

### 发票验证项目

**支持XML发票和PDF发票验证，包括：**

1. **发票抬头验证**: 自动识别发票类型
   - **个人发票**: 抬头为"产品测试"或"个人产品测试"，无纳税人识别号
   - **企业发票**: 包含企业名称和对应的纳税人识别号
     - 中国太平洋保险（集团）股份有限公司虚拟单位 - 919900000000214267
     - 北京十斤文化传媒有限公司 - 91110101MA01JDE656
   - **政府发票**: 抬头为"仙桃市酒店烹饪协会"等政府机构

2. **保单号验证**: 检查发票备注中是否包含正确的保单号

3. **价税合计验证**: 验证发票金额与保险费是否一致

### 发票URL获取方式

系统通过API自动查询发票URL，无需在CSV文件中手动提供：

**API接口**: `https://back.n.nkomol.cn/api/execute-sql`

**查询SQL**:
```sql
SELECT trade_serial_no, invoice_url, xml_url
FROM callbacks
WHERE callback_type_raw=5 AND trade_serial_no='交易流水号'
```

**请求示例**:
```bash
curl --location --request POST 'https://back.n.nkomol.cn/api/execute-sql' \
--header 'Remote-Host: 127.0.0.1' \
--header 'User-Agent: Apifox/1.0.0 (https://apifox.com)' \
--header 'Content-Type: application/json' \
--header 'Accept: */*' \
--header 'Host: back.n.nkomol.cn' \
--header 'Connection: keep-alive' \
--data-raw '{
    "sql": "SELECT trade_serial_no, invoice_url, xml_url FROM callbacks WHERE callback_type_raw=5 AND trade_serial_no='\''TC1944585862039027713'\''"
}'
```

## 输出文件

- **下载的PDF文件**: 保存在 `downloaded_pdfs/` 目录下，按保单号命名
- **下载的发票文件**: XML和PDF发票文件也保存在 `downloaded_pdfs/` 目录下
- **验证报告**: 根据验证模式生成不同的报告文件
  - `validation_report_policy.txt` - 保单验证报告
  - `validation_report_invoice.txt` - 发票验证报告
  - `validation_report_both.txt` - 综合验证报告

## 项目结构

```
pdf_test/
├── pdf_validator.py         # 主程序文件（支持境内险和境外险）
├── test_validator.py        # 基础功能测试脚本
├── test_overseas_format.py  # 境外险格式兼容性测试脚本
├── test_invoice_validation.py # 发票验证功能测试脚本
├── test_data.csv           # 境内险测试数据文件
├── test_overseas.csv       # 境外险测试数据文件
├── test_invoice_data.csv   # 发票验证测试数据文件（旧版本，包含URL字段）
├── test_api_invoice_data.csv # API查询发票验证测试数据文件
├── product.csv             # 产品数据文件
├── 团险投保3款产品.csv       # 团险产品数据文件
├── downloaded_pdfs/        # PDF下载目录
├── validation_report.txt   # 验证报告
└── README.md              # 说明文档
```

## 主要类和方法

### PDFValidator类

**保单验证方法：**
- `__init__(csv_path)`: 初始化验证器
- `read_csv_data()`: 读取CSV文件数据
- `query_policy_info(trade_serial_no)`: 查询保单信息
- `download_pdf(policy_no, download_url)`: 下载PDF文件
- `extract_pdf_info(pdf_path)`: 提取PDF文本信息
- `validate_policy(pdf_text, policy_info, csv_row)`: 验证保单信息

**发票验证方法：**
- `query_invoice_urls(trade_serial_no)`: 通过API查询发票URL
- `download_invoice_xml(xml_url, filename_prefix)`: 下载XML发票
- `download_invoice_pdf(pdf_url, filename_prefix)`: 下载PDF发票
- `parse_xml_invoice(xml_content)`: 解析XML发票内容
- `extract_invoice_pdf_info(pdf_path)`: 提取PDF发票文本
- `validate_invoice(invoice_data, policy_info, csv_row, invoice_type)`: 验证发票信息
- `is_personal_invoice()`: 判断个人发票
- `is_enterprise_invoice()`: 判断企业发票
- `is_government_invoice()`: 判断政府发票

**主要处理方法：**
- `process_policy(csv_row, validation_mode)`: 处理单个保单/发票
- `process_invoice(csv_row, policy_info)`: 处理发票验证
- `process_all_policies(validation_mode)`: 处理所有记录
- `generate_report(results, validation_mode)`: 生成验证报告

## 错误处理

程序包含完善的错误处理机制：
- 网络请求超时和重试
- PDF文件下载失败处理
- PDF文本提取失败处理
- 数据格式验证
- 详细的错误日志记录

## 注意事项

1. 确保网络连接正常，能够访问PDF下载链接
2. CSV文件编码应为UTF-8
3. 程序会自动创建下载目录
4. 如果PDF文件已存在，会跳过下载直接使用本地文件
5. 验证规则可以根据实际需求进行调整

## 测试

运行基础功能测试：
```bash
python test_validator.py
```

运行境外险格式兼容性测试：
```bash
python test_overseas_format.py
```

运行发票验证功能测试：
```bash
python test_invoice_validation.py
```

运行发票API查询测试：
```bash
python test_invoice_api.py
```

测试不同验证模式：
```bash
# 测试保单验证
python pdf_validator.py test_data.csv policy

# 测试发票验证（使用API查询）
python pdf_validator.py test_api_invoice_data.csv invoice

# 测试综合验证（使用API查询）
python pdf_validator.py test_api_invoice_data.csv both
```

## 许可证

本项目仅供学习和测试使用。
