#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试发票验证功能
"""

from pdf_validator import PDFValidator

def test_xml_invoice_parsing():
    """测试XML发票解析功能"""
    print("测试XML发票解析功能")
    print("=" * 50)
    
    # 模拟XML发票内容
    xml_content = '''<?xml version="1.0" encoding="utf-8" standalone="yes"?> 
<EInvoice>
  <Header>
    <EIid>25317000001523000890</EIid>
  </Header>
  <EInvoiceData>
    <SellerInformation>
      <SellerIdNum>913100007362379322</SellerIdNum>
      <SellerName>中国太平洋财产保险股份有限公司上海分公司</SellerName>
    </SellerInformation>
    <BuyerInformation>
      <BuyerIdNum>91110101MA01JDE656</BuyerIdNum>
      <BuyerName>北京十斤文化传媒有限公司</BuyerName>
    </BuyerInformation>
    <BasicInformation>
      <TotalTax-includedAmount>11.2</TotalTax-includedAmount>
    </BasicInformation>
    <AdditionalInformation>
      <Remark>保单号:ASHH53014X25FN00BM5V等</Remark>
    </AdditionalInformation>
  </EInvoiceData>
  <TaxSupervisionInfo>
    <InvoiceNumber>25317000001523000890</InvoiceNumber>
  </TaxSupervisionInfo>
</EInvoice>'''
    
    try:
        validator = PDFValidator('test_invoice_data.csv')
        invoice_info = validator.parse_xml_invoice(xml_content)
        
        print("解析结果:")
        for key, value in invoice_info.items():
            print(f"  {key}: {value}")
            
        print("\n✅ XML发票解析测试通过")
        
    except Exception as e:
        print(f"❌ XML发票解析测试失败: {str(e)}")

def test_invoice_validation():
    """测试发票验证功能"""
    print("\n测试发票验证功能")
    print("=" * 50)
    
    validator = PDFValidator('test_invoice_data.csv')
    
    # 模拟保单信息
    policy_info = {
        'queryData': {
            'policyNo': 'ASHH53014X25FN00BM5V'
        }
    }
    
    # 模拟CSV行数据
    csv_row = {
        'fixPremium': '11.20'
    }
    
    # 模拟XML发票数据
    xml_invoice_data = {
        'buyer_name': '北京十斤文化传媒有限公司',
        'buyer_id': '91110101MA01JDE656',
        'total_amount': '11.2',
        'remark': '保单号:ASHH53014X25FN00BM5V等'
    }
    
    try:
        # 测试XML发票验证
        xml_result = validator.validate_invoice(xml_invoice_data, policy_info, csv_row, "xml")
        print("XML发票验证结果:")
        print(xml_result)
        
        print("\n✅ 发票验证测试通过")
        
    except Exception as e:
        print(f"❌ 发票验证测试失败: {str(e)}")

def test_invoice_types():
    """测试不同类型发票的识别"""
    print("\n测试发票类型识别")
    print("=" * 50)
    
    validator = PDFValidator('test_invoice_data.csv')
    
    # 测试个人发票
    print("1. 个人发票测试:")
    personal_result = validator.is_personal_invoice("产品测试", "")
    print(f"   产品测试 (无纳税人识别号): {'✅' if personal_result else '❌'}")
    
    # 测试企业发票
    print("\n2. 企业发票测试:")
    enterprise_result = validator.is_enterprise_invoice(
        "中国太平洋保险（集团）股份有限公司虚拟单位", 
        "919900000000214267"
    )
    print(f"   太保虚拟单位: {'✅' if enterprise_result else '❌'}")
    
    enterprise_result2 = validator.is_enterprise_invoice(
        "北京十斤文化传媒有限公司", 
        "91110101MA01JDE656"
    )
    print(f"   北京十斤文化传媒: {'✅' if enterprise_result2 else '❌'}")
    
    # 测试政府发票
    print("\n3. 政府发票测试:")
    government_result = validator.is_government_invoice("仙桃市酒店烹饪协会", "")
    print(f"   仙桃市酒店烹饪协会: {'✅' if government_result else '❌'}")

def test_pdf_invoice_validation():
    """测试PDF发票验证"""
    print("\n测试PDF发票验证")
    print("=" * 50)
    
    # 模拟PDF发票文本
    pdf_text = '''电子发票（普通发票） 发票号码：25997000000182333583 开票日期：2025年07月14日 
购 销 
买 售 
名称：中国太平洋保险（集团）股份有限公司虚拟单位 名称：中国太平洋保险（集团）股份有限公司 
方 方 
信 统一社会信用代码/纳税人识别号：919900000000214267 信 统一社会信用代码/纳税人识别号：91310000132211707B 
息 息 
项目名称 规格型号 单 位 数 量 单 价 金 额 税率/征收率 税 额 
*橡胶制品*旅行人身意外 ** 单 1 1.51 1.51 6% 0.09 
伤害保险（2023版互联网 ） 
合 计 ¥1.51 ¥0.09 
价税合计（大写） 壹圆陆角 （小写）¥1.60 
保单号:ASHH08014X25FN000BK5 
备 注 
开票人:太保财险'''
    
    validator = PDFValidator('test_invoice_data.csv')
    
    # 模拟保单信息
    policy_info = {
        'queryData': {
            'policyNo': 'ASHH08014X25FN000BK5'
        }
    }
    
    # 模拟CSV行数据
    csv_row = {
        'fixPremium': '1.60'
    }
    
    try:
        # 测试PDF发票验证
        pdf_result = validator.validate_invoice(pdf_text, policy_info, csv_row, "pdf")
        print("PDF发票验证结果:")
        print(pdf_result)
        
        print("\n✅ PDF发票验证测试通过")
        
    except Exception as e:
        print(f"❌ PDF发票验证测试失败: {str(e)}")

if __name__ == "__main__":
    test_xml_invoice_parsing()
    test_invoice_validation()
    test_invoice_types()
    test_pdf_invoice_validation()
    print("\n所有测试完成!")
