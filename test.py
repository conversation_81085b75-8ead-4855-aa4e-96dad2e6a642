import requests
import pdfplumber
import io
import re

def download_and_extract_pdf(url):
    # 下载PDF文件
    response = requests.get(url, stream=True)
    response.raise_for_status()  # 检查请求是否成功
    
    # 创建内存中的PDF文件对象
    pdf_file = io.BytesIO(response.content)
    
    # 存储每页文本的列表
    page_texts = []
    
    try:
        with pdfplumber.open(pdf_file) as pdf:
            print(f"PDF总页数: {len(pdf.pages)}")
            
            # 逐页处理
            for page_number, page in enumerate(pdf.pages, start=1):
                # 只输出前两页
                if page_number > 4:
                    break
                # 提取文本并清理
                text = page.extract_text()
                if text:
                    # 删除多余空格和特殊字符
                    cleaned_text = re.sub(r'\s+', ' ', text).strip()
                    page_texts.append(f"--- 第 {page_number} 页 ---\n{cleaned_text}")
                else:
                    page_texts.append(f"--- 第 {page_number} 页 ---\n[无法提取文本，可能是扫描件]")
    
    except Exception as e:
        return [f"处理PDF时出错: {str(e)}"]
    
    return page_texts

# 示例使用
if __name__ == "__main__":
    pdf_url = input("请输入PDF文件URL: ")
    pages = download_and_extract_pdf(pdf_url)
    
    # 输出每页文本
    for page_content in pages:
        print("\n" + page_content + "\n")