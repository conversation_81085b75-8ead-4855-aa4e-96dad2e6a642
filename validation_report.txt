PDF保单验证报告
==================================================
生成时间: 2025-07-14 12:45:27
总记录数: 40
成功处理: 16 条
处理失败: 24 条


记录 1:
------------------------------

记录 2:
------------------------------

记录 3:
------------------------------

记录 4:
------------------------------

记录 5:
------------------------------

记录 6:
------------------------------

记录 7:
------------------------------

记录 8:
------------------------------

记录 9:
------------------------------

记录 10:
------------------------------

记录 11:
------------------------------

记录 12:
------------------------------

记录 13:
------------------------------

记录 14:
------------------------------

记录 15:
------------------------------

记录 16:
------------------------------

记录 17:
------------------------------

记录 18:
------------------------------

记录 19:
------------------------------

记录 20:
------------------------------

记录 21:
------------------------------

记录 22:
------------------------------

记录 23:
------------------------------

记录 24:
------------------------------

记录 25:
------------------------------
状态: ✅ 成功
交易流水号: TC1944585838123106305
保单号: ASHH08014X25FN000BK5
PDF文件: .\downloaded_pdfs\ASHH08014X25FN000BK5.pdf
验证结果:
✅ 保单号验证通过
✅ 投保人名称验证通过
✅ 投保人证件信息验证通过
✅ 旅游目的地验证通过: 杭州
✅ 保险费验证通过: 1.6元
✅ 保险期间验证通过: 2025-07-15 至 2025-07-16
✅ 销售机构验证通过
CSV数据: {'数据集名称': '太保研学旅意险-计划A', 'planCode': '00200008301', 'fixPremium': '1.6', 'effectTime': '{{$date.now|addDays(1)|startOfDay}}', 'expiryTime': '{{$date.now|addDays(2)|startOfDay}}', 'destination': '杭州', 'remark': '1.0.9 新增', 'tradeSerialNo': 'TC1944585838123106305'}

记录 26:
------------------------------
状态: ✅ 成功
交易流水号: TC1944585841000398850
保单号: ASHH08014X25FN000BK6
PDF文件: .\downloaded_pdfs\ASHH08014X25FN000BK6.pdf
验证结果:
✅ 保单号验证通过
✅ 投保人名称验证通过
✅ 投保人证件信息验证通过
✅ 旅游目的地验证通过: 杭州, 苏州
✅ 保险费验证通过: 6.4元
✅ 保险期间验证通过: 2025-07-15 至 2025-07-22
✅ 销售机构验证通过
CSV数据: {'数据集名称': '太保研学旅意险-计划A', 'planCode': '00200008301', 'fixPremium': '6.4', 'effectTime': '{{$date.now|addDays(1)|startOfDay}}', 'expiryTime': '{{$date.now|addDays(8)|startOfDay}}', 'destination': '杭州,苏州', 'remark': '', 'tradeSerialNo': 'TC1944585841000398850'}

记录 27:
------------------------------
状态: ✅ 成功
交易流水号: TC1944585843881885697
保单号: ASHH08014X25FN000BK7
PDF文件: .\downloaded_pdfs\ASHH08014X25FN000BK7.pdf
验证结果:
✅ 保单号验证通过
✅ 投保人名称验证通过
✅ 投保人证件信息验证通过
✅ 旅游目的地验证通过: 杭州
✅ 保险费验证通过: 3元
✅ 保险期间验证通过: 2025-07-15 至 2025-07-16
✅ 销售机构验证通过
CSV数据: {'数据集名称': '太保研学旅意险-计划B', 'planCode': '00200008302', 'fixPremium': '3', 'effectTime': '{{$date.now|addDays(1)|startOfDay}}', 'expiryTime': '{{$date.now|addDays(2)|startOfDay}}', 'destination': '杭州', 'remark': '', 'tradeSerialNo': 'TC1944585843881885697'}

记录 28:
------------------------------
状态: ✅ 成功
交易流水号: TC1944585846692069378
保单号: ASHH08014X25FN000BK8
PDF文件: .\downloaded_pdfs\ASHH08014X25FN000BK8.pdf
验证结果:
✅ 保单号验证通过
✅ 投保人名称验证通过
✅ 投保人证件信息验证通过
✅ 旅游目的地验证通过: 杭州, 苏州
✅ 保险费验证通过: 12元
✅ 保险期间验证通过: 2025-07-15 至 2025-07-22
✅ 销售机构验证通过
CSV数据: {'数据集名称': '太保研学旅意险-计划B', 'planCode': '00200008302', 'fixPremium': '12', 'effectTime': '{{$date.now|addDays(1)|startOfDay}}', 'expiryTime': '{{$date.now|addDays(8)|startOfDay}}', 'destination': '杭州,苏州', 'remark': '', 'tradeSerialNo': 'TC1944585846692069378'}

记录 29:
------------------------------
状态: ✅ 成功
交易流水号: TC1944585849972015106
保单号: ASHH08014X25FN000BK9
PDF文件: .\downloaded_pdfs\ASHH08014X25FN000BK9.pdf
验证结果:
✅ 保单号验证通过
✅ 投保人名称验证通过
✅ 投保人证件信息验证通过
✅ 旅游目的地验证通过: 蒙古 Mongolia
✅ 保险费验证通过: 7.5元
✅ 保险期间验证通过: 2025-07-15 至 2025-07-16
✅ 销售机构验证通过
CSV数据: {'数据集名称': '太平洋亚洲畅游旅行险-A基础版', 'planCode': '00200008401', 'fixPremium': '7.5', 'effectTime': '{{$date.now|addDays(1)|startOfDay}}', 'expiryTime': '{{$date.now|addDays(2)|startOfDay}}', 'destination': '蒙古 Mongolia', 'remark': '', 'tradeSerialNo': 'TC1944585849972015106'}

记录 30:
------------------------------
状态: ✅ 成功
交易流水号: TC1944585853260349441
保单号: ASHH08014X25FN000BKA
PDF文件: .\downloaded_pdfs\ASHH08014X25FN000BKA.pdf
验证结果:
✅ 保单号验证通过
✅ 投保人名称验证通过
✅ 投保人证件信息验证通过
✅ 旅游目的地验证通过: 蒙古 Mongolia, 日本 Japan
✅ 保险费验证通过: 30元
✅ 保险期间验证通过: 2025-07-15 至 2025-09-08
✅ 销售机构验证通过
CSV数据: {'数据集名称': '太平洋亚洲畅游旅行险-A基础版', 'planCode': '00200008401', 'fixPremium': '30', 'effectTime': '{{$date.now|addDays(1)|startOfDay}}', 'expiryTime': '{{$date.now|addDays(56)|startOfDay}}', 'destination': '蒙古 Mongolia,日本 Japan', 'remark': '', 'tradeSerialNo': 'TC1944585853260349441'}

记录 31:
------------------------------
状态: ✅ 成功
交易流水号: TC1944585856452214785
保单号: ASHH08014X25FN000BKB
PDF文件: .\downloaded_pdfs\ASHH08014X25FN000BKB.pdf
验证结果:
✅ 保单号验证通过
✅ 投保人名称验证通过
✅ 投保人证件信息验证通过
✅ 旅游目的地验证通过: 蒙古 Mongolia
✅ 保险费验证通过: 12.5元
✅ 保险期间验证通过: 2025-07-15 至 2025-07-16
✅ 销售机构验证通过
CSV数据: {'数据集名称': '太平洋亚洲畅游旅行险-B升级版', 'planCode': '00200008402', 'fixPremium': '12.5', 'effectTime': '{{$date.now|addDays(1)|startOfDay}}', 'expiryTime': '{{$date.now|addDays(2)|startOfDay}}', 'destination': '蒙古 Mongolia', 'remark': '', 'tradeSerialNo': 'TC1944585856452214785'}

记录 32:
------------------------------
状态: ✅ 成功
交易流水号: TC1944585859233038338
保单号: ASHH08014X25FN000BKC
PDF文件: .\downloaded_pdfs\ASHH08014X25FN000BKC.pdf
验证结果:
✅ 保单号验证通过
✅ 投保人名称验证通过
✅ 投保人证件信息验证通过
✅ 旅游目的地验证通过: 蒙古 Mongolia, 日本 Japan
❌ 保险费验证失败: PDF中未找到 45元
✅ 保险期间验证通过: 2025-07-15 至 2025-07-31
✅ 销售机构验证通过
CSV数据: {'数据集名称': '太平洋亚洲畅游旅行险-B升级版', 'planCode': '00200008402', 'fixPremium': '45', 'effectTime': '{{$date.now|addDays(1)|startOfDay}}', 'expiryTime': '{{$date.now|addDays(17)|startOfDay}}', 'destination': '蒙古 Mongolia,日本 Japan', 'remark': '', 'tradeSerialNo': 'TC1944585859233038338'}

记录 33:
------------------------------
状态: ✅ 成功
交易流水号: TC1944585862039027713
保单号: ASHH08014X25FN000BKD
PDF文件: .\downloaded_pdfs\ASHH08014X25FN000BKD.pdf
验证结果:
✅ 保单号验证通过
✅ 投保人名称验证通过
✅ 投保人证件信息验证通过
✅ 旅游目的地验证通过: 蒙古 Mongolia
✅ 保险费验证通过: 75元
✅ 保险期间验证通过: 2025-07-15 至 2025-07-16
✅ 销售机构验证通过
CSV数据: {'数据集名称': '太平洋亚洲畅游旅行险-C高端版', 'planCode': '00200008403', 'fixPremium': '75', 'effectTime': '{{$date.now|addDays(1)|startOfDay}}', 'expiryTime': '{{$date.now|addDays(2)|startOfDay}}', 'destination': '蒙古 Mongolia', 'remark': '', 'tradeSerialNo': 'TC1944585862039027713'}

记录 34:
------------------------------
状态: ✅ 成功
交易流水号: TC1944585865167978497
保单号: ASHH08014X25FN000BKE
PDF文件: .\downloaded_pdfs\ASHH08014X25FN000BKE.pdf
验证结果:
✅ 保单号验证通过
✅ 投保人名称验证通过
✅ 投保人证件信息验证通过
✅ 旅游目的地验证通过: 蒙古 Mongolia, 日本 Japan
✅ 保险费验证通过: 380元
✅ 保险期间验证通过: 2025-07-15 至 2025-09-13
✅ 销售机构验证通过
CSV数据: {'数据集名称': '太平洋亚洲畅游旅行险-C高端版', 'planCode': '00200008403', 'fixPremium': '380', 'effectTime': '{{$date.now|addDays(1)|startOfDay}}', 'expiryTime': '{{$date.now|addDays(61)|startOfDay}}', 'destination': '蒙古 Mongolia,日本 Japan', 'remark': '', 'tradeSerialNo': 'TC1944585865167978497'}

记录 35:
------------------------------
状态: ✅ 成功
交易流水号: TC1944585868301123585
保单号: ASHH08014X25FN000BKF
PDF文件: .\downloaded_pdfs\ASHH08014X25FN000BKF.pdf
验证结果:
✅ 保单号验证通过
✅ 投保人名称验证通过
✅ 投保人证件信息验证通过
✅ 旅游目的地验证通过: 杭州
✅ 保险费验证通过: 6.1元
✅ 保险期间验证通过: 2025-07-15 至 2025-07-16
✅ 销售机构验证通过
CSV数据: {'数据集名称': '境内自驾游保险-计划A', 'planCode': '00200008501', 'fixPremium': '6.1', 'effectTime': '{{$date.now|addDays(1)|startOfDay}}', 'expiryTime': '{{$date.now|addDays(2)|startOfDay}}', 'destination': '杭州', 'remark': '', 'tradeSerialNo': 'TC1944585868301123585'}

记录 36:
------------------------------
状态: ✅ 成功
交易流水号: TC1944585871497183233
保单号: ASHH08014X25FN000BKG
PDF文件: .\downloaded_pdfs\ASHH08014X25FN000BKG.pdf
验证结果:
✅ 保单号验证通过
✅ 投保人名称验证通过
✅ 投保人证件信息验证通过
✅ 旅游目的地验证通过: 嘉兴, 苏州
✅ 保险费验证通过: 159元
✅ 保险期间验证通过: 2025-07-15 至 2025-10-14
✅ 销售机构验证通过
CSV数据: {'数据集名称': '境内自驾游保险-计划A', 'planCode': '00200008501', 'fixPremium': '159', 'effectTime': '{{$date.now|addDays(1)|startOfDay}}', 'expiryTime': '{{$date.now|addDays(92)|startOfDay}}', 'destination': '嘉兴,苏州', 'remark': '', 'tradeSerialNo': 'TC1944585871497183233'}

记录 37:
------------------------------
状态: ✅ 成功
交易流水号: TC1944585874642911233
保单号: ASHH08014X25FN000BKH
PDF文件: .\downloaded_pdfs\ASHH08014X25FN000BKH.pdf
验证结果:
✅ 保单号验证通过
✅ 投保人名称验证通过
✅ 投保人证件信息验证通过
✅ 旅游目的地验证通过: 杭州
✅ 保险费验证通过: 7.5元
✅ 保险期间验证通过: 2025-07-15 至 2025-07-16
✅ 销售机构验证通过
CSV数据: {'数据集名称': '境内自驾游保险-计划B', 'planCode': '00200008502', 'fixPremium': '7.5', 'effectTime': '{{$date.now|addDays(1)|startOfDay}}', 'expiryTime': '{{$date.now|addDays(2)|startOfDay}}', 'destination': '杭州', 'remark': '', 'tradeSerialNo': 'TC1944585874642911233'}

记录 38:
------------------------------
状态: ✅ 成功
交易流水号: TC1944585878266789890
保单号: ASHH08014X25FN000BKI
PDF文件: .\downloaded_pdfs\ASHH08014X25FN000BKI.pdf
验证结果:
✅ 保单号验证通过
✅ 投保人名称验证通过
✅ 投保人证件信息验证通过
✅ 旅游目的地验证通过: 嘉兴, 苏州
✅ 保险费验证通过: 220元
✅ 保险期间验证通过: 2025-07-15 至 2025-10-17
✅ 销售机构验证通过
CSV数据: {'数据集名称': '境内自驾游保险-计划B', 'planCode': '00200008502', 'fixPremium': '220', 'effectTime': '{{$date.now|addDays(1)|startOfDay}}', 'expiryTime': '{{$date.now|addDays(95)|startOfDay}}', 'destination': '嘉兴,苏州', 'remark': '', 'tradeSerialNo': 'TC1944585878266789890'}

记录 39:
------------------------------
状态: ✅ 成功
交易流水号: TC1944585881488015362
保单号: ASHH08014X25FN000BKJ
PDF文件: .\downloaded_pdfs\ASHH08014X25FN000BKJ.pdf
验证结果:
✅ 保单号验证通过
✅ 投保人名称验证通过
✅ 投保人证件信息验证通过
✅ 旅游目的地验证通过: 杭州
✅ 保险费验证通过: 16.3元
✅ 保险期间验证通过: 2025-07-15 至 2025-07-16
✅ 销售机构验证通过
CSV数据: {'数据集名称': '境内自驾游保险-计划C', 'planCode': '00200008503', 'fixPremium': '16.3', 'effectTime': '{{$date.now|addDays(1)|startOfDay}}', 'expiryTime': '{{$date.now|addDays(2)|startOfDay}}', 'destination': '杭州', 'remark': '', 'tradeSerialNo': 'TC1944585881488015362'}

记录 40:
------------------------------
状态: ✅ 成功
交易流水号: TC1944585884742795266
保单号: ASHH08014X25FN000BKK
PDF文件: .\downloaded_pdfs\ASHH08014X25FN000BKK.pdf
验证结果:
✅ 保单号验证通过
✅ 投保人名称验证通过
✅ 投保人证件信息验证通过
✅ 旅游目的地验证通过: 嘉兴, 苏州
✅ 保险费验证通过: 455元
✅ 保险期间验证通过: 2025-07-15 至 2025-11-12
✅ 销售机构验证通过
CSV数据: {'数据集名称': '境内自驾游保险-计划C', 'planCode': '00200008503', 'fixPremium': '455', 'effectTime': '{{$date.now|addDays(1)|startOfDay}}', 'expiryTime': '{{$date.now|addDays(121)|startOfDay}}', 'destination': '嘉兴,苏州', 'remark': '', 'tradeSerialNo': 'TC1944585884742795266'}
