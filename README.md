# PDF保单验证器

这是一个用于验证PDF保单信息的Python工具，可以从CSV文件读取保单数据，下载对应的PDF文件，并验证PDF内容与保单信息的一致性。

## 功能特性

- 📄 从CSV文件读取保单数据
- 🔍 查询保单信息（模拟API调用）
- ⬇️ 自动下载PDF保单文件
- 📝 提取PDF文本内容（前4页）
- ✅ 验证保单信息一致性
- 📊 生成详细的验证报告

## 安装依赖

```bash
pip install requests pdfplumber
```

## 使用方法

### 命令行使用

```bash
python pdf_validator.py <csv_file_path>
```

示例：
```bash
python pdf_validator.py test_data.csv
python pdf_validator.py 团险投保3款产品.csv
```

### CSV文件格式

CSV文件应包含以下字段：
- `数据集名称`: 产品名称
- `planCode`: 计划代码
- `fixPremium`: 保险费
- `effectTime`: 生效时间
- `expiryTime`: 到期时间
- `destination`: 旅游目的地
- `remark`: 备注
- `tradeSerialNo`: 交易流水号（可选，如果为空会自动生成）

### 验证项目

程序会验证以下内容，**支持境内险和境外险两种格式**：

1. **保单号验证**: 检查PDF中是否包含正确的保单号
2. **投保人名称验证**: 验证投保人名称为"产品测试"
   - 境内险格式：`投保人名称：产品测试`
   - 境外险格式：`投保人名称 Applicant： 产品测试`
3. **投保人证件信息验证**: 验证身份证号码
   - 境内险格式：`投保人证件类型：身份证` + `投保人证件号码：530102194410193109`
   - 境外险格式：`投保人证件类型 ID Type of the Applicant： 身份证 National ID` + `投保人证件号码 ID No： 530102194410193109`
4. **旅游目的地验证**: 检查PDF中是否包含指定的目的地
5. **保险费验证**: 验证保险费金额
   - 境内险格式：`保险费：人民币X元X角整（RMB: X.XX元）`
   - 境外险格式：`保险费 Total Premium： 人民币 X元X角整（RMB: X.XX元）`
6. **保险期间验证**: 验证保险生效和到期时间
   - 境内险格式：`保险期间：自（From）2025年07月15日 至（To）2025年07月16日`
   - 境外险格式：`保险期间 Insured Period： 自（From）2025-07-15 00:00起至（To）2025-07-16 00:00止`
7. **销售机构验证**: 验证销售机构名称
   - 境内险格式：`委托销售机构/营销员名称：泰昌保险销售有限公司`
   - 境外险格式：`委托销售机构/营销员名称 Agent：泰昌保险销售有限公司`

## 输出文件

- **下载的PDF文件**: 保存在 `downloaded_pdfs/` 目录下，按保单号命名
- **验证报告**: 生成 `validation_report.txt` 文件，包含详细的验证结果

## 项目结构

```
pdf_test/
├── pdf_validator.py         # 主程序文件（支持境内险和境外险）
├── test_validator.py        # 基础功能测试脚本
├── test_overseas_format.py  # 境外险格式兼容性测试脚本
├── test_data.csv           # 境内险测试数据文件
├── test_overseas.csv       # 境外险测试数据文件
├── product.csv             # 产品数据文件
├── 团险投保3款产品.csv       # 团险产品数据文件
├── downloaded_pdfs/        # PDF下载目录
├── validation_report.txt   # 验证报告
└── README.md              # 说明文档
```

## 主要类和方法

### PDFValidator类

- `__init__(csv_path)`: 初始化验证器
- `read_csv_data()`: 读取CSV文件数据
- `query_policy_info(trade_serial_no)`: 查询保单信息
- `download_pdf(policy_no, download_url)`: 下载PDF文件
- `extract_pdf_info(pdf_path)`: 提取PDF文本信息
- `validate_policy(pdf_text, policy_info, csv_row)`: 验证保单信息
- `process_policy(csv_row)`: 处理单个保单
- `process_all_policies()`: 处理所有保单
- `generate_report(results)`: 生成验证报告

## 错误处理

程序包含完善的错误处理机制：
- 网络请求超时和重试
- PDF文件下载失败处理
- PDF文本提取失败处理
- 数据格式验证
- 详细的错误日志记录

## 注意事项

1. 确保网络连接正常，能够访问PDF下载链接
2. CSV文件编码应为UTF-8
3. 程序会自动创建下载目录
4. 如果PDF文件已存在，会跳过下载直接使用本地文件
5. 验证规则可以根据实际需求进行调整

## 测试

运行基础功能测试：
```bash
python test_validator.py
```

运行境外险格式兼容性测试：
```bash
python test_overseas_format.py
```

测试境外险数据处理：
```bash
python pdf_validator.py test_overseas.csv
```

## 许可证

本项目仅供学习和测试使用。
