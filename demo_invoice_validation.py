#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
发票验证功能演示脚本
展示如何使用新的发票验证功能
"""

import os
from pdf_validator import PDFValidator

def demo_validation_modes():
    """演示不同的验证模式"""
    print("发票验证功能演示")
    print("=" * 60)
    
    # 创建演示用的CSV数据
    demo_csv_content = '''数据集名称,planCode,fixPremium,effectTime,expiryTime,destination,remark,tradeSerialNo,xmlUrl,pdfUrl
演示产品A,00200007601,1.60,2025-07-15,2025-07-16,杭州,演示数据,demo_serial_001,https://example.com/invoice1.xml,https://example.com/invoice1.pdf
演示产品B,00200007602,11.20,2025-07-15,2025-07-16,上海,演示数据,demo_serial_002,https://example.com/invoice2.xml,
演示产品C,00200007603,25.50,2025-07-15,2025-07-16,北京,演示数据,demo_serial_003,,https://example.com/invoice3.pdf'''
    
    # 保存演示CSV文件
    demo_csv_path = 'demo_data.csv'
    with open(demo_csv_path, 'w', encoding='utf-8') as f:
        f.write(demo_csv_content)
    
    print(f"创建演示CSV文件: {demo_csv_path}")
    print("\nCSV文件内容:")
    print(demo_csv_content)
    
    # 创建验证器实例
    validator = PDFValidator(demo_csv_path)
    
    print("\n" + "=" * 60)
    print("1. 演示保单验证模式 (policy)")
    print("-" * 40)
    
    try:
        # 读取CSV数据
        csv_data = validator.read_csv_data()
        print(f"读取到 {len(csv_data)} 条记录")
        
        # 显示第一条记录的字段
        if csv_data:
            print("\n第一条记录的字段:")
            for key, value in csv_data[0].items():
                print(f"  {key}: {value}")
    
    except Exception as e:
        print(f"演示过程中出错: {str(e)}")
    
    print("\n" + "=" * 60)
    print("2. 发票类型识别演示")
    print("-" * 40)
    
    # 演示不同类型发票的识别
    test_cases = [
        ("个人发票", "产品测试", ""),
        ("企业发票", "中国太平洋保险（集团）股份有限公司虚拟单位", "919900000000214267"),
        ("企业发票", "北京十斤文化传媒有限公司", "91110101MA01JDE656"),
        ("政府发票", "仙桃市酒店烹饪协会", ""),
        ("未知类型", "其他公司", "123456789")
    ]
    
    for case_type, buyer_name, buyer_id in test_cases:
        is_personal = validator.is_personal_invoice(buyer_name, buyer_id)
        is_enterprise = validator.is_enterprise_invoice(buyer_name, buyer_id)
        is_government = validator.is_government_invoice(buyer_name, buyer_id)
        
        result = "未知"
        if is_personal:
            result = "个人发票"
        elif is_enterprise:
            result = "企业发票"
        elif is_government:
            result = "政府发票"
        
        print(f"  {case_type}: {buyer_name} -> {result}")
    
    print("\n" + "=" * 60)
    print("3. XML发票解析演示")
    print("-" * 40)
    
    # 演示XML发票解析
    sample_xml = '''<?xml version="1.0" encoding="utf-8"?>
<EInvoice>
  <EInvoiceData>
    <BuyerInformation>
      <BuyerName>产品测试</BuyerName>
      <BuyerIdNum></BuyerIdNum>
    </BuyerInformation>
    <BasicInformation>
      <TotalTax-includedAmount>1.60</TotalTax-includedAmount>
    </BasicInformation>
    <AdditionalInformation>
      <Remark>保单号:DEMO123456789</Remark>
    </AdditionalInformation>
  </EInvoiceData>
  <TaxSupervisionInfo>
    <InvoiceNumber>12345678901234567890</InvoiceNumber>
  </TaxSupervisionInfo>
</EInvoice>'''
    
    try:
        invoice_info = validator.parse_xml_invoice(sample_xml)
        print("XML发票解析结果:")
        for key, value in invoice_info.items():
            print(f"  {key}: {value}")
    except Exception as e:
        print(f"XML解析失败: {str(e)}")
    
    print("\n" + "=" * 60)
    print("4. 发票验证演示")
    print("-" * 40)
    
    # 模拟发票验证
    mock_policy_info = {
        'queryData': {
            'policyNo': 'DEMO123456789'
        }
    }
    
    mock_csv_row = {
        'fixPremium': '1.60'
    }
    
    mock_invoice_data = {
        'buyer_name': '产品测试',
        'buyer_id': '',
        'total_amount': '1.60',
        'remark': '保单号:DEMO123456789'
    }
    
    try:
        validation_result = validator.validate_invoice(
            mock_invoice_data, 
            mock_policy_info, 
            mock_csv_row, 
            "xml"
        )
        print("发票验证结果:")
        print(validation_result)
    except Exception as e:
        print(f"发票验证失败: {str(e)}")
    
    # 清理演示文件
    if os.path.exists(demo_csv_path):
        os.remove(demo_csv_path)
        print(f"\n清理演示文件: {demo_csv_path}")
    
    print("\n" + "=" * 60)
    print("演示完成!")
    print("\n使用说明:")
    print("1. 准备包含xmlUrl和pdfUrl字段的CSV文件")
    print("2. 运行命令: python pdf_validator.py your_file.csv invoice")
    print("3. 查看生成的验证报告")

if __name__ == "__main__":
    demo_validation_modes()
